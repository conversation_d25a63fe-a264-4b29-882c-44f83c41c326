# 📊 Ambiente de Análise Financeira

Este projeto está configurado com `uv` para análises financeiras usando Python.

## 🚀 Bibliotecas Instaladas

- **yfinance**: Para obter dados financeiros do Yahoo Finance
- **pandas**: Para manipulação e análise de dados
- **numpy**: Para computação numérica
- **matplotlib**: Para criação de gráficos
- **seaborn**: Para visualizações estatísticas avançadas
- **scipy**: Para computação científica e estatística

## 📋 Como Usar

### Executar o teste do ambiente:
```bash
uv run python main.py
```

### Executar análises:
```bash
# Exemplo geral de análise
uv run python exemplo_analise.py

# ⭐ GERAR GRÁFICOS DE AÇÕES BRASILEIRAS (RECOMENDADO):

# Gráficos das 20 principais ações (já executado com sucesso)
uv run python gerar_graficos_simples.py

# Gráficos de TODAS as 73 ações solicitadas
uv run python todas_acoes_73.py

# Outros scripts:
uv run python acoes_brasileiras.py      # Análise interativa
uv run python graficos_rapidos.py       # Gráficos rápidos
uv run python exemplo_uso.py            # Tutorial
```

### Instalar novas bibliotecas:
```bash
uv add nome_da_biblioteca
```

### Executar scripts Python:
```bash
uv run python seu_script.py
```

### Ativar o ambiente virtual (opcional):
```bash
source .venv/bin/activate
```

## 📈 Scripts Disponíveis

### `exemplo_analise.py`
Exemplo completo de análise financeira com funções para:
- Obter dados históricos de ações
- Calcular métricas financeiras básicas
- Criar gráficos de preços e volume
- Comparar performance de múltiplas ações

### `acoes_brasileiras.py` 🇧🇷
Script especializado para análise de ações brasileiras:
- **73 ações brasileiras** pré-configuradas
- Gráficos comparativos e individuais
- Ranking de performance do último ano
- Análise completa com TOP 10

### `graficos_rapidos.py` ⚡
Script para gráficos rápidos das principais ações:
- **15 principais ações** brasileiras
- Comparação por setores (Bancos, Commodities, Consumo, Infraestrutura)
- Resumo de performance
- Interface simples e rápida

## 🔧 Estrutura do Projeto

```
finance/
├── main.py                 # Teste do ambiente
├── exemplo_analise.py      # Exemplo de análise financeira
├── acoes_brasileiras.py    # Análise de 73 ações brasileiras
├── graficos_rapidos.py     # Gráficos rápidos das principais ações
├── pyproject.toml          # Configuração do projeto
├── uv.lock                # Lock file das dependências
└── README.md              # Este arquivo
```

## 💡 Dicas

- Use `uv run` para executar scripts sem ativar o ambiente virtual
- O `uv` gerencia automaticamente as dependências e o ambiente virtual
- Para análises interativas, considere usar Jupyter notebooks: `uv add jupyter`